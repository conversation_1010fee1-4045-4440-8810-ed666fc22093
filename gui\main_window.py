"""
Main window for the Cold Emailer GUI application - Clean Minimalistic Version
"""
import sys
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QTabWidget, QVBoxLayout, QWidget,
    QMessageBox, QLabel, QStatusBar, QHBoxLayout
)
from PyQt6.QtGui import QAction, QFont
from PyQt6.QtCore import Qt
import qtawesome as qta
from utils import create_default_dirs
from PyQt6.QtWidgets import QScrollArea

# Import tab components
from gui.components.email_sender_tab import EmailSenderTab
from gui.components.contacts_tab import ContactsTab
from gui.components.templates_tab import TemplatesTab
from gui.components.settings_tab import SettingsTab

class MainWindow(QMainWindow):
    """Main window for the Cold Emailer GUI application"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Cold Emailer")
        self.resize(900, 650)  # More reasonable default size

        # Create default directories
        create_default_dirs()

        self.init_ui()

        # Set application icon
        self.setWindowIcon(qta.icon('fa5s.envelope'))
        
        # Create the central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Create clean header
        header_widget = QWidget()
        header_widget.setObjectName("headerWidget")
        header_layout = QVBoxLayout(header_widget)
        header_layout.setContentsMargins(24, 20, 24, 16)
        header_layout.setSpacing(4)
        
        header_label = QLabel("Cold Emailer")
        header_label.setFont(QFont("SF Pro Display", 24, QFont.Weight.DemiBold))
        header_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_label.setObjectName("headerTitle")
        header_layout.addWidget(header_label)

        # Simple subtitle
        subtitle_label = QLabel("Send personalized emails")
        subtitle_label.setFont(QFont("SF Pro Display", 13, QFont.Weight.Normal))
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        subtitle_label.setObjectName("headerSubtitle")
        header_layout.addWidget(subtitle_label)
        
        main_layout.addWidget(header_widget)
        
        # Create tab widget with minimal styling
        tab_container = QWidget()
        tab_layout = QVBoxLayout(tab_container)
        tab_layout.setContentsMargins(16, 0, 16, 16)
        
        self.tabs = QTabWidget()
        self.tabs.setFont(QFont("SF Pro Display", 10, QFont.Weight.Medium))
        self.tabs.setTabPosition(QTabWidget.TabPosition.North)
        self.tabs.setDocumentMode(True)
        self.tabs.setObjectName("mainTabs")
        
        # Create tabs
        self.contacts_tab = ContactsTab()  # Create ContactsTab first
        self.email_sender_tab = EmailSenderTab(self.contacts_tab) # Pass contacts_tab to EmailSenderTab
        self.templates_tab = TemplatesTab()
        self.settings_tab = SettingsTab()
        
        # Add tabs with simple icons
        self.add_tab(self.email_sender_tab,
                    qta.icon('fa5s.paper-plane', color='#6b7280'),
                    "Send")
        self.add_tab(self.contacts_tab,
                    qta.icon('fa5s.address-book', color='#6b7280'),
                    "Contacts")
        self.add_tab(self.templates_tab,
                    qta.icon('fa5s.file-alt', color='#6b7280'),
                    "Templates")
        self.add_tab(self.settings_tab,
                    qta.icon('fa5s.cog', color='#6b7280'),
                    "Settings")
        
        tab_layout.addWidget(self.tabs)
        main_layout.addWidget(tab_container)
        
        # Clean status bar
        self.status_bar = QStatusBar()
        self.status_bar.setObjectName("cleanStatusBar")
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Ready")
        
        # Set up menu bar
        self.setup_menu()
        
        # Apply clean stylesheet
        self.apply_clean_stylesheet()
    
    def init_ui(self):
        self.setMinimumSize(400, 500)  # Much smaller minimum size
    
    def setup_menu(self):
        """Set up the application menu bar"""
        menu_bar = self.menuBar()
        menu_bar.setObjectName("cleanMenuBar")
        
        # File menu
        file_menu = menu_bar.addMenu("File")
        
        # Exit action
        exit_action = QAction(qta.icon('fa5s.sign-out-alt', color='#6b7280'), "Exit", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Help menu
        help_menu = menu_bar.addMenu("Help")
        
        # About action
        about_action = QAction(qta.icon('fa5s.info-circle', color='#6b7280'), "About", self)
        about_action.triggered.connect(self.show_about_dialog)
        help_menu.addAction(about_action)
        
        # Documentation action
        docs_action = QAction(qta.icon('fa5s.book', color='#6b7280'), "Documentation", self)
        docs_action.triggered.connect(self.show_documentation)
        help_menu.addAction(docs_action)

    def add_tab(self, widget, icon, title):
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setWidget(widget)
        self.tabs.addTab(scroll, icon, title)
    
    def show_about_dialog(self):
        """Show about dialog"""
        QMessageBox.about(
            self,
            "About Cold Emailer",
            "Cold Emailer v1.0\n\n"
            "A clean, minimal email automation tool.\n\n"
            "© 2025 Cold Emailer"
        )
    
    def show_documentation(self):
        """Show documentation dialog"""
        QMessageBox.information(
            self,
            "Documentation",
            "Cold Emailer Documentation\n\n"
            "Visit: https://github.com/AndrxwWxng/cold-emailer\n\n"
            "Quick Start:\n"
            "1. Configure email settings\n"
            "2. Add contacts\n"
            "3. Create templates\n"
            "4. Send emails"
        )
    
    def apply_clean_stylesheet(self):
        """Apply a clean, minimal dark theme stylesheet"""
        self.setStyleSheet("""
            /* Base Styling */
            QMainWindow {
                background-color: #111827;
                color: #f9fafb;
            }
            
            QWidget {
                background-color: transparent;
                color: #f9fafb;
                font-family: "SF Pro Display", "Segoe UI", -apple-system, BlinkMacSystemFont, sans-serif;
            }
            
            /* Header */
            QWidget#headerWidget {
                background-color: #1f2937;
                border-bottom: 1px solid #374151;
            }
            
            QLabel#headerTitle {
                color: #f9fafb;
                background: transparent;
            }
            
            QLabel#headerSubtitle {
                color: #9ca3af;
                background: transparent;
            }
            
            /* Tab Widget - Clean and Minimal */
            QTabWidget#mainTabs::pane {
                border: 1px solid #374151;
                border-radius: 8px;
                background-color: #1f2937;
                margin-top: -1px;
            }
            
            QTabBar::tab {
                background-color: transparent;
                border: none;
                padding: 12px 20px;
                margin-right: 1px;
                color: #9ca3af;
                font-weight: 500;
                font-size: 12px;
                min-width: 60px;
                border-radius: 6px 6px 0px 0px;
            }
            
            QTabBar::tab:selected {
                background-color: #1f2937;
                color: #f9fafb;
                border-bottom: 2px solid #3b82f6;
            }
            
            QTabBar::tab:hover:!selected {
                background-color: #374151;
                color: #f9fafb;
            }
            
            /* Clean Button Styling - Single Color */
            QPushButton {
                background-color: #3b82f6;
                color: #ffffff;
                border: none;
                border-radius: 6px;
                padding: 10px 16px;
                font-weight: 500;
                font-size: 14px;
                min-height: 36px;
            }
            
            QPushButton:hover {
                background-color: #2563eb;
            }
            
            QPushButton:pressed {
                background-color: #1d4ed8;
            }
            
            QPushButton:disabled {
                background-color: #4b5563;
                color: #9ca3af;
            }
            
            /* Success Button */
            QPushButton#test_button {
                background-color: #10b981;
            }
            
            QPushButton#test_button:hover {
                background-color: #059669;
            }
            
            QPushButton#test_button:pressed {
                background-color: #047857;
            }
            
            /* Clean Input Fields */
            QLineEdit, QTextEdit, QComboBox {
                border: 1px solid #4b5563;
                border-radius: 6px;
                padding: 8px 12px;
                background-color: transparent;
                color: #f9fafb;
                font-size: 14px;
                min-height: 20px;
            }
            
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus {
                border: 2px solid #3b82f6;
                background-color: rgba(59, 130, 246, 0.05);
            }
            
            QLineEdit:read-only {
                background-color: transparent;
                color: #9ca3af;
                border: 1px solid #374151;
            }
            
            /* Spin Box */
            QSpinBox {
                border: 1px solid #4b5563;
                border-radius: 6px;
                padding: 8px 12px;
                background-color: transparent;
                color: #f9fafb;
                font-size: 14px;
                min-height: 20px;
            }
            
            QSpinBox:focus {
                border: 2px solid #3b82f6;
            }
            
            QSpinBox::up-button, QSpinBox::down-button {
                border: none;
                border-radius: 3px;
                background-color: #4b5563;
                width: 20px;
                margin: 1px;
            }
            
            QSpinBox::up-button:hover, QSpinBox::down-button:hover {
                background-color: #6b7280;
            }
            
            QSpinBox::up-button:pressed, QSpinBox::down-button:pressed {
                background-color: #374151;
            }
            
            /* Labels - Clean and Simple */
            QLabel {
                color: #f9fafb;
                font-weight: 400;
                font-size: 14px;
                background: transparent;
            }
            
            /* Group Box */
            QGroupBox {
                border: 1px solid #4b5563;
                border-radius: 8px;
                margin-top: 12px;
                padding-top: 16px;
                font-weight: 600;
                font-size: 15px;
                background-color: transparent;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 12px;
                padding: 0 8px;
                color: #f9fafb;
                font-weight: 600;
                background-color: #111827;
            }
            
            /* Progress Bar */
            QProgressBar {
                border: none;
                border-radius: 4px;
                background-color: #374151;
                text-align: center;
                min-height: 16px;
                color: #f9fafb;
                font-weight: 500;
                font-size: 12px;
            }
            
            QProgressBar::chunk {
                background-color: #3b82f6;
                border-radius: 4px;
            }
            
            /* Checkbox */
            QCheckBox {
                spacing: 8px;
                color: #f9fafb;
                font-size: 14px;
                font-weight: 400;
            }
            
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 3px;
                border: 2px solid #4b5563;
                background-color: transparent;
            }
            
            QCheckBox::indicator:checked {
                background-color: #3b82f6;
                border: 2px solid #3b82f6;
            }
            
            QCheckBox::indicator:hover {
                border: 2px solid #6b7280;
            }
            
            /* Scrollbar */
            QScrollBar:vertical {
                border: none;
                background-color: #374151;
                width: 12px;
                border-radius: 6px;
                margin: 0;
            }
            
            QScrollBar::handle:vertical {
                background-color: #6b7280;
                min-height: 20px;
                border-radius: 6px;
                margin: 2px;
            }
            
            QScrollBar::handle:vertical:hover {
                background-color: #9ca3af;
            }
            
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
            
            /* ComboBox Dropdown */
            QComboBox::drop-down {
                border: none;
                border-radius: 4px;
                background-color: #4b5563;
                width: 24px;
                margin: 2px;
            }
            
            QComboBox::drop-down:hover {
                background-color: #6b7280;
            }
            
            QComboBox::down-arrow {
                width: 8px;
                height: 8px;
                border: 2px solid #f9fafb;
                border-top: none;
                border-left: none;
            }
            
            /* List and Table Widgets */
            QListWidget, QTableWidget {
                background-color: transparent;
                border: 1px solid #4b5563;
                border-radius: 6px;
                color: #f9fafb;
                gridline-color: #374151;
                alternate-background-color: #1f2937;
            }
            
            QListWidget::item, QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #374151;
            }
            
            QListWidget::item:selected, QTableWidget::item:selected {
                background-color: #3b82f6;
                color: #ffffff;
            }
            
            QListWidget::item:hover, QTableWidget::item:hover {
                background-color: #374151;
            }
            
            QHeaderView::section {
                background-color: #374151;
                color: #f9fafb;
                padding: 8px 12px;
                border: 1px solid #4b5563;
                font-weight: 600;
                font-size: 13px;
            }
            
            /* Status Bar */
            QStatusBar#cleanStatusBar {
                background-color: #1f2937;
                color: #9ca3af;
                border-top: 1px solid #374151;
                font-weight: 400;
                font-size: 12px;
            }
            
            /* Menu Bar */
            QMenuBar#cleanMenuBar {
                background-color: #1f2937;
                color: #f9fafb;
                border-bottom: 1px solid #374151;
                font-weight: 400;
            }
            
            QMenuBar::item {
                background-color: transparent;
                padding: 8px 12px;
                border-radius: 4px;
                margin: 2px 1px;
            }
            
            QMenuBar::item:selected {
                background-color: #374151;
            }
            
            QMenu {
                background-color: #1f2937;
                color: #f9fafb;
                border: 1px solid #4b5563;
                border-radius: 6px;
            }
            
            QMenu::item {
                padding: 8px 16px;
                border-radius: 4px;
                margin: 2px 4px;
            }
            
            QMenu::item:selected {
                background-color: #3b82f6;
                color: #ffffff;
            }
            
            /* Message Box Styling */
            QMessageBox {
                background-color: #1f2937;
                color: #f9fafb;
            }
            
            QMessageBox QPushButton {
                min-width: 80px;
                padding: 8px 16px;
            }
            
            /* Responsive Design - Smaller Screens */
            QTabBar::tab {
                padding: 8px 12px;
                min-width: 50px;
                font-size: 11px;
            }
            
            /* Adjust for very small windows */
            QTabBar::tab {
                /* keep padding, remove hard min-width */
                padding: 8px 12px;
                font-size: 11px;
            }
        """)

def main():
    """Main entry point for the clean GUI application"""
    app = QApplication(sys.argv)
    app.setStyle("Fusion")  
    
    app.setApplicationName("Cold Emailer")
    app.setOrganizationName("Andrew Wang")
    
    # Set clean system font
    app.setFont(QFont("SF Pro Display", 10, QFont.Weight.Normal))
    
    window = MainWindow()
    window.show()

    sys.exit(app.exec())

if __name__ == "__main__":
        main()