"""
Email Sender Tab for Cold Emailer GUI
"""
import pandas as pd
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QComboBox,
    QLineEdit, QSpinBox, QCheckBox, QFileDialog, QProgressBar, QTextEdit,
    QGroupBox, QFormLayout, QMessageBox, QSplitter
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
import qtawesome as qta

# Import the email sender module
from email_sender import EmailSender
from template_manager import TemplateManager
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QSplitter, QScrollArea
)
from PyQt6.QtCore import Qt

class EmailSenderWorker(QThread):
    """Worker thread for sending emails"""
    progress_update = pyqtSignal(int, str)
    finished = pyqtSignal(int, int)
    error = pyqtSignal(str)
    
    def __init__(self, email_sender, template_manager, template_id, contacts_df, test_mode, limit):
        super().__init__()
        self.email_sender = email_sender
        self.template_manager = template_manager
        self.template_id = template_id
        self.contacts_df = contacts_df # Store DataFrame directly
        self.test_mode = test_mode
        self.limit = limit
    
    def run(self):
        try:
            # Use the provided DataFrame
            df = self.contacts_df
            if df is None or df.empty:
                self.error.emit("No contacts loaded to send emails to.")
                return
            
            # Apply limit if specified
            if self.limit is not None and self.limit > 0:
                df = df.head(self.limit)
            
            total_contacts = len(df)
            successful_emails = 0
            
            # Process each contact
            for index, row in df.iterrows():
                try:
                    # Extract contact information
                    full_name = row.get('Professor Name', '')
                    email = row.get('Professor Email', '')
                    research_area = row.get('Research Area', '')
                    
                    # Skip if email is missing
                    if pd.isna(email) or not email:
                        self.progress_update.emit(index, f"Skipping {full_name} due to missing email")
                        continue
                    
                    # Update progress
                    self.progress_update.emit(index, f"Processing {full_name} at {email}...")
                    
                    # Prepare variables for template
                    variables = {
                        'full_name': full_name,
                        'email': email,
                        'research_area': research_area
                    }
                    
                    # Send email
                    if self.test_mode:
                        # In test mode, just simulate sending
                        self.progress_update.emit(index, f"[TEST] Would send to {full_name} at {email}")
                        successful_emails += 1
                    else:
                        # Get template
                        template = self.template_manager.get_template(self.template_id)
                        if not template:
                            self.error.emit(f"Template '{self.template_id}' not found")
                            return

                        # Personalize template
                        subject = template.get('subject', '')
                        body = template.get('body', '')

                        # Replace variables
                        for var, value in variables.items():
                            subject = subject.replace(f"{{{var}}}", str(value))
                            body = body.replace(f"{{{var}}}", str(value))

                        # Send actual email
                        result = self.email_sender.send_email(
                            to_email=email,
                            subject=subject,
                            body=body
                        )
                        if result['success']:
                            successful_emails += 1
                            self.progress_update.emit(index, f"Successfully sent to {full_name}")
                        else:
                            error_msg = result.get('error', 'Unknown error')
                            self.progress_update.emit(index, f"Failed to send to {full_name}: {error_msg}")
                
                except Exception as e:
                    self.progress_update.emit(index, f"Error processing {email}: {str(e)}")
            
            # Emit finished signal with statistics
            self.finished.emit(successful_emails, total_contacts)
            
        except Exception as e:
            self.error.emit(f"Error sending emails: {str(e)}")

class EmailSenderTab(QWidget):
    def __init__(self, contacts_tab): # Accept contacts_tab instance
        super().__init__()
        self.email_sender = EmailSender()
        self.template_manager = TemplateManager()
        self.contacts_tab = contacts_tab # Store contacts_tab instance
        self.init_ui()
    
    def init_ui(self):
        """Initialize the user interface"""
        # Main layout with optimized spacing for smaller screens
        outer_layout = QVBoxLayout(self)
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        container = QWidget()
        scroll.setWidget(container)
        outer_layout.addWidget(scroll)

        layout = QVBoxLayout(container)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(16)

        # Responsive: stack vertically on small screens, horizontally on large
        self.responsive_widget = QWidget()
        self.responsive_layout = QHBoxLayout(self.responsive_widget)
        self.responsive_layout.setContentsMargins(0, 0, 0, 0)
        self.responsive_layout.setSpacing(16)

        # ===== LEFT SIDE - CONFIGURATION =====
        config_group = QGroupBox("Email Configuration")
        config_layout = QFormLayout(config_group)
        config_layout.setSpacing(12)
        config_layout.setContentsMargins(16, 24, 16, 16)
        config_layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.ExpandingFieldsGrow)
        config_layout.setRowWrapPolicy(QFormLayout.RowWrapPolicy.WrapLongRows)

        # Template selection
        template_label = QLabel("Email Template:")
        template_layout = QHBoxLayout()
        template_layout.setSpacing(8)
        self.template_combo = QComboBox()
        self.template_combo.setMinimumHeight(36)
        self.refresh_templates()
        template_layout.addWidget(self.template_combo, 1)
        self.refresh_button = QPushButton(qta.icon('fa5s.sync'), "Refresh")
        self.refresh_button.setMinimumHeight(36)
        self.refresh_button.setMinimumWidth(80)
        self.refresh_button.clicked.connect(self.refresh_templates)
        template_layout.addWidget(self.refresh_button)
        config_layout.addRow(template_label, template_layout)

        # Contacts will be sourced from the Contacts Tab
        contacts_info_label = QLabel("Contacts will be sourced directly from the 'Contacts' tab.")
        contacts_info_label.setStyleSheet("font-style: italic; color: #888;")
        config_layout.addRow("", contacts_info_label)

        # Email limit
        limit_label = QLabel("Limit:")
        self.limit_spin = QSpinBox()
        self.limit_spin.setRange(0, 1000)
        self.limit_spin.setValue(0)
        self.limit_spin.setSpecialValueText("No limit")
        self.limit_spin.setMinimumHeight(36)
        self.limit_spin.setMinimumWidth(100)
        config_layout.addRow(limit_label, self.limit_spin)

        # Test mode checkbox
        self.test_mode_check = QCheckBox("Test Mode (don't actually send emails)")
        self.test_mode_check.setChecked(True)
        config_layout.addRow("", self.test_mode_check)

        # Test connection button
        self.test_button = QPushButton(qta.icon('fa5s.vial'), "Test Connection")
        self.test_button.setObjectName("test_button")
        self.test_button.setMinimumHeight(36)
        self.test_button.clicked.connect(self.test_email_connection)
        config_layout.addRow("", self.test_button)

        # ===== RIGHT SIDE - PREVIEW =====
        preview_group = QGroupBox("Email Preview")
        preview_layout = QVBoxLayout(preview_group)
        preview_layout.setSpacing(10)
        preview_layout.setContentsMargins(16, 24, 16, 16)
        # Subject preview
        subject_layout = QFormLayout()
        subject_layout.setSpacing(8)
        subject_label = QLabel("Subject:")
        self.subject_label = QLabel("N/A")
        self.subject_label.setWordWrap(True)
        self.subject_label.setMinimumHeight(36)
        subject_layout.addRow(subject_label, self.subject_label)
        preview_layout.addLayout(subject_layout)
        # Body preview
        body_label = QLabel("Body:")
        preview_layout.addWidget(body_label)
        self.body_preview = QTextEdit()
        self.body_preview.setReadOnly(True)
        self.body_preview.setMinimumHeight(200)
        preview_layout.addWidget(self.body_preview)
        # Update preview when template changes
        self.template_combo.currentTextChanged.connect(self.update_preview)
        self.template_combo.currentIndexChanged.connect(self.update_preview)

        # Add config and preview to responsive layout
        self.responsive_layout.addWidget(config_group, 1)
        self.responsive_layout.addWidget(preview_group, 2)
        layout.addWidget(self.responsive_widget)

        # Bottom section - Progress
        progress_group = QGroupBox("Progress")
        progress_layout = QVBoxLayout(progress_group)
        progress_layout.setSpacing(10)
        progress_layout.setContentsMargins(16, 24, 16, 16)
        progress_label = QLabel("Status:")
        progress_layout.addWidget(progress_label)
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setMinimumHeight(16)
        self.progress_bar.setTextVisible(True)
        progress_layout.addWidget(self.progress_bar)
        log_label = QLabel("Log:")
        progress_layout.addWidget(log_label)
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setMinimumHeight(120)
        progress_layout.addWidget(self.log_text)
        layout.addWidget(progress_group)

        # Action button
        self.send_button = QPushButton(qta.icon('fa5s.paper-plane'), "Send Emails")
        self.send_button.setMinimumWidth(180)
        self.send_button.setMinimumHeight(42)
        self.send_button.clicked.connect(self.send_emails)
        send_button_layout = QHBoxLayout()
        send_button_layout.addStretch()
        send_button_layout.addWidget(self.send_button)
        send_button_layout.addStretch()
        layout.addLayout(send_button_layout)

        # Update the preview initially
        self.update_preview()
    
    def refresh_templates(self):
        """Refresh the templates list"""
        self.template_combo.clear()
        templates = self.template_manager.get_templates()

        if not templates:
            # Add a placeholder if no templates found
            self.template_combo.addItem("No templates found", None)
            return

        for template in templates:
            # Use template ID as the data and name as display text
            template_name = template.get('name', template.get('id', 'Unknown'))
            template_id = template.get('id', template.get('name', ''))
            self.template_combo.addItem(template_name, template_id)

        # Select first template by default
        if self.template_combo.count() > 0:
            self.template_combo.setCurrentIndex(0)
    
    def update_preview(self):
        """Update the email preview based on the selected template"""
        # Get template ID from combo box data
        template_id = self.template_combo.currentData()
        if not template_id:
            self.subject_label.setText("No template selected")
            self.body_preview.setText("Please select a template to preview the email content.")
            return

        template = self.template_manager.get_template(template_id)
        if not template:
            self.subject_label.setText("Template not found")
            self.body_preview.setText("Selected template could not be loaded.")
            return

        # Update subject
        subject = template.get('subject', 'No subject')
        self.subject_label.setText(subject)

        # Update body with proper formatting
        body = template.get('body', 'No body content')
        # Replace \n with actual line breaks for display
        formatted_body = body.replace('\\n', '\n')
        self.body_preview.setText(formatted_body)

        # Set cursor to beginning
        cursor = self.body_preview.textCursor()
        cursor.movePosition(cursor.MoveOperation.Start)
        self.body_preview.setTextCursor(cursor)
    
    def send_emails(self):
        """Send emails based on the current configuration"""
        template_id = self.template_combo.currentData()
        test_mode = self.test_mode_check.isChecked()
        limit = self.limit_spin.value() if self.limit_spin.value() > 0 else None

        # Validate inputs
        if not template_id:
            QMessageBox.warning(self, "Missing Template", "Please select an email template.")
            return
        
        # Get contacts DataFrame from ContactsTab
        contacts_df = self.contacts_tab.get_current_data_as_df()
        if contacts_df is None or contacts_df.empty:
            QMessageBox.warning(self, "No Contacts", "No contacts found in the Contacts tab. Please add or load contacts first.")
            return
        
        # Confirm sending
        if not test_mode:
            confirm = QMessageBox.question(
                self,
                "Confirm Send",
                f"You are about to send real emails to contacts in {contacts_file}.\n\n"
                f"Are you sure you want to continue?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            if confirm != QMessageBox.StandardButton.Yes:
                return
        
        # Clear log and reset progress
        self.log_text.clear()
        self.progress_bar.setValue(0)
        
        # Disable send button during operation
        self.send_button.setEnabled(False)
        
        # Create and start worker thread
        self.worker = EmailSenderWorker(
            self.email_sender,
            self.template_manager,
            template_id,
            contacts_df, # Pass DataFrame directly
            test_mode,
            limit
        )
        
        # Connect signals
        self.worker.progress_update.connect(self.update_progress)
        self.worker.finished.connect(self.sending_finished)
        self.worker.error.connect(self.handle_error)
        
        # Start the worker
        self.worker.start()
        
        # Log start
        mode = "TEST MODE" if test_mode else "LIVE MODE"
        template_name = self.template_combo.currentText()
        self.log(f"Starting email sending in {mode}")
        self.log(f"Template: {template_name}")
        self.log(f"Contacts: {contacts_file}")
        if limit:
            self.log(f"Limit: {limit} contacts")
        self.log("-----------------------------------")
    
    def update_progress(self, index, message):
        """Update progress bar and log"""
        # Update progress bar based on index
        # Note: We don't have total count here, so we'll update it in the worker
        _ = index  # Suppress unused parameter warning
        self.log(message)

    def resizeEvent(self, event):
        super().resizeEvent(event)
        # Responsive: stack vertically if width < 700px
        if self.width() < 700:
            if isinstance(self.responsive_layout, QHBoxLayout):
                # Replace with QVBoxLayout
                widgets = [self.responsive_layout.itemAt(i).widget() for i in range(self.responsive_layout.count())]
                for w in widgets:
                    self.responsive_layout.removeWidget(w)
                self.responsive_layout.deleteLater()
                self.responsive_layout = QVBoxLayout(self.responsive_widget)
                self.responsive_layout.setContentsMargins(0, 0, 0, 0)
                self.responsive_layout.setSpacing(16)
                for w in widgets:
                    self.responsive_layout.addWidget(w)
        else:
            if isinstance(self.responsive_layout, QVBoxLayout):
                widgets = [self.responsive_layout.itemAt(i).widget() for i in range(self.responsive_layout.count())]
                for w in widgets:
                    self.responsive_layout.removeWidget(w)
                self.responsive_layout.deleteLater()
                self.responsive_layout = QHBoxLayout(self.responsive_widget)
                self.responsive_layout.setContentsMargins(0, 0, 0, 0)
                self.responsive_layout.setSpacing(16)
                for w in widgets:
                    self.responsive_layout.addWidget(w)
    
    def sending_finished(self, successful, total):
        """Handle completion of email sending"""
        self.progress_bar.setValue(100)
        self.log("-----------------------------------")
        self.log(f"Email sending complete: {successful} of {total} emails sent successfully")
        self.send_button.setEnabled(True)
        
        QMessageBox.information(
            self,
            "Email Sending Complete",
            f"Successfully sent {successful} of {total} emails."
        )
    
    def handle_error(self, error_message):
        """Handle errors during email sending"""
        self.log(f"ERROR: {error_message}")
        self.send_button.setEnabled(True)
        
        QMessageBox.critical(
            self,
            "Error Sending Emails",
            error_message
        )
    
    def log(self, message):
        """Add a message to the log"""
        self.log_text.append(message)
        # Scroll to bottom
        self.log_text.verticalScrollBar().setValue(self.log_text.verticalScrollBar().maximum())
        
    def test_email_connection(self):
        """Test the email connection without sending an actual email"""
        # Disable the test button during testing
        self.test_button.setEnabled(False)
        self.test_button.setText("Testing...")
        
        # Clear log
        self.log_text.clear()
        self.log("Testing email connection...")
        
        try:
            # Get the email settings from the email sender
            settings = self.email_sender.get_settings()
            
            if not settings.get('smtp_server') or not settings.get('smtp_port'):
                self.log("ERROR: SMTP server settings not configured")
                self.log("Please go to Settings tab and configure your email settings")
                QMessageBox.warning(
                    self,
                    "Settings Not Configured",
                    "Email settings are not properly configured.\n\n"
                    "Please go to the Settings tab and configure your SMTP server settings."
                )
                return
                
            # Test the connection
            self.log(f"Connecting to {settings.get('smtp_server')}:{settings.get('smtp_port')}...")
            
            # Attempt to connect to the SMTP server
            result = self.email_sender.test_connection()
            
            if result['success']:
                self.log("✓ Connection successful!")
                self.log(f"Connected to {settings.get('smtp_server')}:{settings.get('smtp_port')}")
                self.log(f"Authentication method: {settings.get('auth_method', 'Standard')}")
                self.log(f"From email: {settings.get('from_email')}")
                
                QMessageBox.information(
                    self,
                    "Connection Successful",
                    f"Successfully connected to {settings.get('smtp_server')}\n\n"
                    f"Your email settings are correctly configured."
                )
            else:
                self.log(f"✗ Connection failed: {result.get('error', 'Unknown error')}")
                
                QMessageBox.critical(
                    self,
                    "Connection Failed",
                    f"Failed to connect to {settings.get('smtp_server')}\n\n"
                    f"Error: {result.get('error', 'Unknown error')}\n\n"
                    "Please check your email settings in the Settings tab."
                )
        except Exception as e:
            self.log(f"✗ Error testing connection: {str(e)}")
            
            QMessageBox.critical(
                self,
                "Connection Error",
                f"An error occurred while testing the connection:\n\n{str(e)}"
            )
        finally:
            # Re-enable the test button
            self.test_button.setEnabled(True)
            self.test_button.setText("Test Connection")
