"""
Contacts Tab for Cold Emailer GUI - Clean Professional Version
"""
import pandas as pd
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QLineEdit, QFileDialog, QTableWidget, QTableWidgetItem, QHeaderView,
    QGroupBox, QFormLayout, QMessageBox, QDialog, QDialogButtonBox
)
from contacts_manager import ContactsManager

class AddContactDialog(QDialog):
    """Clean dialog for adding contacts"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Add Contact")
        self.setMinimumWidth(400)
        self.init_ui()
    
    def init_ui(self):
        """Initialize clean dialog UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        
        form_layout = QFormLayout()
        form_layout.setSpacing(15)
        
        self.professor_name_edit = QLineEdit()
        self.professor_name_edit.setPlaceholderText("<PERSON><PERSON> <PERSON>")
        form_layout.addRow("Professor Name:", self.professor_name_edit)
        
        self.professor_email_edit = QLineEdit()
        self.professor_email_edit.setPlaceholderText("<EMAIL>")
        form_layout.addRow("Professor Email:", self.professor_email_edit)
        
        self.research_area_edit = QLineEdit()
        self.research_area_edit.setPlaceholderText("Machine Learning, AI")
        form_layout.addRow("Research Area:", self.research_area_edit)
        
        layout.addLayout(form_layout)
        
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
    
    def get_contact_data(self):
        """Get contact data"""
        return {
            'professor_name': self.professor_name_edit.text().strip(),
            'professor_email': self.professor_email_edit.text().strip(),
            'research_area': self.research_area_edit.text().strip()
        }

class ContactsTab(QWidget):
    """Clean contacts management tab"""
    
    def __init__(self):
        super().__init__()
        self.contacts_manager = ContactsManager()
        self.current_file = None
        self.init_ui()
    
    def init_ui(self):
        """Initialize clean UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # File Management
        file_group = QGroupBox("File Management")
        file_layout = QVBoxLayout(file_group)
        file_layout.setSpacing(15)
        
        # File selection
        file_select_layout = QHBoxLayout()
        self.file_edit = QLineEdit()
        self.file_edit.setPlaceholderText("Select or create a contacts file")
        self.file_edit.setReadOnly(True)
        file_select_layout.addWidget(self.file_edit, 1)
        
        browse_button = QPushButton("Browse")
        browse_button.clicked.connect(self.browse_contacts_file)
        file_select_layout.addWidget(browse_button)
        
        new_file_button = QPushButton("New File")
        new_file_button.clicked.connect(self.create_new_file)
        file_select_layout.addWidget(new_file_button)
        
        file_layout.addLayout(file_select_layout)
        layout.addWidget(file_group)
        
        # Contacts Table
        table_group = QGroupBox("Contacts")
        table_layout = QVBoxLayout(table_group)
        table_layout.setSpacing(15)
        
        self.contacts_table = QTableWidget()
        self.contacts_table.setColumnCount(3)
        self.contacts_table.setHorizontalHeaderLabels(
            ["Professor Name", "Professor Email", "Research Area"]
        )
        self.contacts_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.contacts_table.setAlternatingRowColors(True)
        self.contacts_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.contacts_table.setMaximumHeight(300)
        
        table_layout.addWidget(self.contacts_table)
        
        # Table Actions
        actions_layout = QHBoxLayout()
        
        self.add_button = QPushButton("Add Contact")
        self.add_button.clicked.connect(self.add_contact)
        actions_layout.addWidget(self.add_button)
        
        self.import_button = QPushButton("Import")
        self.import_button.clicked.connect(self.import_contacts)
        actions_layout.addWidget(self.import_button)
        
        self.export_button = QPushButton("Export")
        self.export_button.clicked.connect(self.export_contacts)
        actions_layout.addWidget(self.export_button)
        
        actions_layout.addStretch()
        
        self.delete_button = QPushButton("Delete Selected")
        self.delete_button.clicked.connect(self.delete_selected_contacts)
        actions_layout.addWidget(self.delete_button)
        
        table_layout.addLayout(actions_layout)
        layout.addWidget(table_group)
        
        # Statistics
        self.stats_label = QLabel("No contacts loaded")
        layout.addWidget(self.stats_label)
        
        self.update_button_states()
    
    def update_button_states(self):
        """Update button states"""
        has_file = self.current_file is not None
        has_selection = len(self.contacts_table.selectedItems()) > 0
        
        self.add_button.setEnabled(True)
        self.import_button.setEnabled(has_file)
        self.export_button.setEnabled(has_file and self.contacts_table.rowCount() > 0)
        self.delete_button.setEnabled(has_file and has_selection)
    
    def browse_contacts_file(self):
        """Browse for contacts file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Select Contacts File",
            "",
            "Excel Files (*.xlsx *.xls);;CSV Files (*.csv)"
        )
        if file_path:
            self.load_contacts_file(file_path)
            self.update_button_states()
            return True
        self.update_button_states()
        return False
    
    def create_new_file(self):
        """Create new contacts file"""
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Create New Contacts File",
            "",
            "Excel Files (*.xlsx);;CSV Files (*.csv)"
        )
        if file_path:
            df = pd.DataFrame(columns=["Professor Name", "Professor Email", "Research Area"])
            
            if file_path.endswith('.xlsx'):
                df.to_excel(file_path, index=False)
            elif file_path.endswith('.csv'):
                df.to_csv(file_path, index=False)
            
            self.load_contacts_file(file_path)
    
    def load_contacts_file(self, file_path):
        """Load contacts from file"""
        try:
            if file_path.endswith('.xlsx') or file_path.endswith('.xls'):
                df = pd.read_excel(file_path)
            elif file_path.endswith('.csv'):
                df = pd.read_csv(file_path)
            else:
                QMessageBox.warning(self, "Unsupported Format", "File format not supported.")
                return
            
            self.current_file = file_path
            self.file_edit.setText(file_path)
            self.populate_contacts_table(df)
            self.update_button_states()
            
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error loading file: {str(e)}")
    
    def populate_contacts_table(self, df):
        """Populate table with contacts"""
        self.contacts_table.setRowCount(0)

        column_map = {
            'Professor Name': ['Professor Name', 'professor_name', 'name', 'full_name'],
            'Professor Email': ['Professor Email', 'professor_email', 'email'],
            'Research Area': ['Research Area', 'research_area', 'area', 'field']
        }

        actual_columns = {}
        for table_header, possible_names in column_map.items():
            for possible_name in possible_names:
                for df_col in df.columns:
                    if df_col.strip().lower() == possible_name.strip().lower():
                        actual_columns[table_header] = df_col
                        break
                if table_header in actual_columns:
                    break

        for i, row_data in df.iterrows():
            self.contacts_table.insertRow(i)
            for j, header in enumerate(["Professor Name", "Professor Email", "Research Area"]):
                df_col = actual_columns.get(header)
                cell_value = ''
                if df_col and df_col in row_data:
                    raw_value = row_data[df_col]
                    if pd.notna(raw_value):
                        cell_value = str(raw_value).strip()
                self.contacts_table.setItem(i, j, QTableWidgetItem(cell_value))
    
        self.update_statistics()
    
    def update_statistics(self):
        """Update statistics"""
        count = self.contacts_table.rowCount()
        if count == 0:
            self.stats_label.setText("No contacts loaded")
        else:
            self.stats_label.setText(f"{count} contacts loaded")
    
    def add_contact(self):
        """Add new contact"""
        if not self.current_file:
            QMessageBox.information(self, "No File", "Please create or open a contacts file first.")
            if not self.browse_contacts_file():
                if not self.current_file:
                    return

        dialog = AddContactDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            contact_data = dialog.get_contact_data()
            
            if not contact_data['professor_name'] or not contact_data['professor_email']:
                QMessageBox.warning(self, "Required Fields", "Name and email are required.")
                return
            
            row_position = self.contacts_table.rowCount()
            self.contacts_table.insertRow(row_position)
            
            self.contacts_table.setItem(row_position, 0, QTableWidgetItem(contact_data['professor_name']))
            self.contacts_table.setItem(row_position, 1, QTableWidgetItem(contact_data['professor_email']))
            self.contacts_table.setItem(row_position, 2, QTableWidgetItem(contact_data['research_area']))
            
            if self.current_file:
                self.save_contacts()
            
            self.update_statistics()
            self.update_button_states()
    
    def delete_selected_contacts(self):
        """Delete selected contacts"""
        selected_rows = set()
        for item in self.contacts_table.selectedItems():
            selected_rows.add(item.row())
        
        if not selected_rows:
            return
        
        confirm = QMessageBox.question(
            self,
            "Confirm Deletion",
            f"Delete {len(selected_rows)} contact(s)?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if confirm != QMessageBox.StandardButton.Yes:
            return
        
        for row in sorted(selected_rows, reverse=True):
            self.contacts_table.removeRow(row)
        
        if self.current_file:
            self.save_contacts()
        
        self.update_statistics()
    
    def import_contacts(self):
        """Import contacts from file"""
        if not self.current_file:
            QMessageBox.warning(self, "No File", "Please select or create a contacts file first.")
            return
        
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Import Contacts From",
            "",
            "Excel Files (*.xlsx *.xls);;CSV Files (*.csv)"
        )
        
        if not file_path:
            return
        
        try:
            if file_path.endswith('.xlsx') or file_path.endswith('.xls'):
                import_df = pd.read_excel(file_path)
            elif file_path.endswith('.csv'):
                import_df = pd.read_csv(file_path)
            else:
                QMessageBox.warning(self, "Unsupported Format", "File format not supported.")
                return
            
            current_df = self.get_current_data_as_df()
            merged_df = pd.concat([current_df, import_df], ignore_index=True)
            
            self.populate_contacts_table(merged_df)
            self.save_contacts()
            
            QMessageBox.information(self, "Import Complete", f"Imported {len(import_df)} contacts.")
            
        except Exception as e:
            QMessageBox.critical(self, "Import Error", f"Error importing: {str(e)}")
    
    def export_contacts(self):
        """Export contacts to file"""
        if not self.current_file or self.contacts_table.rowCount() == 0:
            QMessageBox.warning(self, "No Contacts", "No contacts to export.")
            return
        
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Export Contacts To",
            "",
            "Excel Files (*.xlsx);;CSV Files (*.csv)"
        )
        
        if not file_path:
            return
        
        try:
            df = self.get_current_data_as_df()
            
            if file_path.endswith('.xlsx'):
                df.to_excel(file_path, index=False)
            elif file_path.endswith('.csv'):
                df.to_csv(file_path, index=False)
            
            QMessageBox.information(self, "Export Complete", f"Exported {len(df)} contacts.")
            
        except Exception as e:
            QMessageBox.critical(self, "Export Error", f"Error exporting: {str(e)}")

    def get_current_data_as_df(self):
        """Get current table data as DataFrame"""
        data = []
        headers = ["Professor Name", "Professor Email", "Research Area"]
        
        for row in range(self.contacts_table.rowCount()):
            row_data = {}
            for col_idx, header in enumerate(headers):
                item = self.contacts_table.item(row, col_idx)
                row_data[header] = item.text() if item else ""
            data.append(row_data)
        
        return pd.DataFrame(data)

    def save_contacts(self):
        """Save contacts to current file"""
        if not self.current_file:
            return
        
        try:
            df = self.get_current_data_as_df()
            
            if self.current_file.endswith('.xlsx'):
                df.to_excel(self.current_file, index=False)
            elif self.current_file.endswith('.csv'):
                df.to_csv(self.current_file, index=False)
            
        except Exception as e:
            QMessageBox.critical(self, "Save Error", f"Error saving: {str(e)}")